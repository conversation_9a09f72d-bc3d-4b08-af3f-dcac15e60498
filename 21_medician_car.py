from libs.PipeLine import <PERSON><PERSON><PERSON>ine
from libs.AIBase import <PERSON>B<PERSON>
from libs.AI2D import Ai2d
from libs.Utils import *
import nncase_runtime as nn
import ulab.numpy as np
import aidemo
from media.display import *
from media.media import *
from media.sensor import *
import time, os, sys, gc,math
import lvgl as lv
from machine import TOUCH
from machine import RTC
import _thread
import utime
from machine import UART
from machine import FPIOA

DISPLAY_WIDTH = ALIGN_UP(800, 16)
DISPLAY_HEIGHT = 480

sensor = None
rgb888p_size=[1280,720]
display_size = [800, 480]
line_flw_stop=False
yolo_det_stop=False
line_osd_img=None
yolo_osd_img=None
lock = _thread.allocate_lock()

#串口0  用于发寻迹  串口1 用于进行 yolo相关的串口收发
uart0 = UART(UART.UART2, baudrate=115200, bits=UART.EIGHTBITS, parity=UART.PARITY_NONE, stop=UART.STOPBITS_ONE)
uart1 = UART(UART.UART3, baudrate=115200, bits=UART.EIGHTBITS, parity=UART.PARITY_NONE, stop=UART.STOPBITS_ONE)

fpioa = FPIOA()
fpioa.set_function(11, FPIOA.UART2_TXD)
fpioa.set_function(12, FPIOA.UART2_RXD)

frame0_flag = 0
frame1_flag = 0
frame2_flag = 1
frame3_flag = 0

# 自定义YOLOv8检测类
class ObjectDetectionApp(AIBase):
    def __init__(self,kmodel_path,labels,model_input_size,max_boxes_num,confidence_threshold=0.8,nms_threshold=0.2,rgb888p_size=[224,224],display_size=[1920,1080],debug_mode=0):
        super().__init__(kmodel_path,model_input_size,rgb888p_size,debug_mode)
        self.kmodel_path=kmodel_path
        self.labels=labels
        self.model_input_size=model_input_size
        self.confidence_threshold=confidence_threshold
        self.nms_threshold=nms_threshold
        self.max_boxes_num=max_boxes_num
        self.rgb888p_size=[ALIGN_UP(rgb888p_size[0],16),rgb888p_size[1]]
        self.display_size=[ALIGN_UP(display_size[0],16),display_size[1]]
        self.debug_mode=debug_mode
        self.color_four=get_colors(len(self.labels))
        self.x_factor = float(self.rgb888p_size[0])/self.model_input_size[0]
        self.y_factor = float(self.rgb888p_size[1])/self.model_input_size[1]
        self.ai2d=Ai2d(debug_mode)
        self.ai2d.set_ai2d_dtype(nn.ai2d_format.NCHW_FMT,nn.ai2d_format.NCHW_FMT,np.uint8, np.uint8)

    # 配置预处理操作，这里使用了resize，Ai2d支持crop/shift/pad/resize/affine，具体代码请打开/sdcard/app/libs/AI2D.py查看
    def config_preprocess(self,input_image_size=None):
        with ScopedTiming("set preprocess config",self.debug_mode > 0):
            ai2d_input_size=input_image_size if input_image_size else self.rgb888p_size
            top,bottom,left,right,self.scale=letterbox_pad_param(self.rgb888p_size,self.model_input_size)
            self.ai2d.pad([0,0,0,0,top,bottom,left,right], 0, [128,128,128])
            self.ai2d.resize(nn.interp_method.tf_bilinear, nn.interp_mode.half_pixel)
            self.ai2d.build([1,3,ai2d_input_size[1],ai2d_input_size[0]],[1,3,self.model_input_size[1],self.model_input_size[0]])

    def postprocess(self,results):
        with ScopedTiming("postprocess",self.debug_mode > 0):
            new_result=results[0][0].transpose()
            det_res = aidemo.yolov8_det_postprocess(new_result.copy(),[self.rgb888p_size[1],self.rgb888p_size[0]],[self.model_input_size[1],self.model_input_size[0]],[self.display_size[1],self.display_size[0]],len(self.labels),self.confidence_threshold,self.nms_threshold,self.max_boxes_num)
            return det_res

    def draw_result(self,osd_img,dets):
        with ScopedTiming("display_draw",self.debug_mode >0):
            osd_img.clear()
            if dets:
                for i in range(len(dets[0])):
                    x, y, w, h = map(lambda x: int(round(x, 0)), dets[0][i])
                    osd_img.draw_rectangle(x,y, w, h, color=self.color_four[dets[1][i]],thickness=4)
                    osd_img.draw_string_advanced(x, y-50,32," " + self.labels[dets[1][i]] + " " + str(round(dets[2][i],2)) , color=self.color_four[dets[1][i]])

    def deinit(self):
        del self.kpu
        del self.ai2d
        self.tensors.clear()
        del self.tensors
        gc.collect()
        time.sleep_ms(50)

def line_following_thread():
    global sensor, line_osd_img, frame1_flag,frame0_flag

    clock = utime.clock()
    #
    # GRAYSCALE_THRESHOLD = [(0, 60)]

    IMAGE_WIDTH = 800
    IMAGE_HEIGHT = 480

    # 多ROI区域配置：[x, y, w, h, weight]
    # ROIS = [
    #     (0, 400, 800, 80, 0.7),  # 底部 ROI，权重大
    #     (0, 200,800, 80, 0.3),  # 中部 ROI
    #     (0, 0, 800, 80, 0.1)  # 顶部 ROI，权重小
    # ]
    ROIS = [
        (0, 400, 800, 80, 0.7),  # 底部 ROI，权重大
        (0, 200,800, 80, 0.3),  # 中部 ROI
        (0, 0, 800, 80, 0.1)  # 顶部 ROI，权重小
    ]
    threshold = [8, 46, 8, 127, -16, 127]

    # 计算权重总和
    weight_sum = 0
    for r in ROIS: weight_sum += r[4]  # r[4] 为矩形权重值.

    while True:
        clock.tick()
        utime.sleep_ms(20)
        if line_flw_stop:
            break

        img = sensor.snapshot(chn=CAM_CHN_ID_1)
        img = img.lens_corr(2.1)
        centroid_sum = 0
        actual_weight_sum = 0  # 动态计算实际权重总和

        line_osd_img.clear()

        # 十字路口检测标志
        crossroad_detected = False

        for i, r in enumerate(ROIS):

            blobs = img.find_blobs([threshold], roi=r[0:4], merge=True,
                                 pixels_threshold=1006, area_threshold=102)

            if blobs:
                largest_blob = max(blobs, key=lambda b: b.pixels())

                line_osd_img.draw_rectangle(largest_blob.rect())

                line_osd_img.draw_cross(largest_blob.cx(), largest_blob.cy())

                blob_rect = largest_blob.rect()
                blob_width = blob_rect[2]  # 修复变量名
                # centroid_sum += largest_blob.cx() * r[4]
                # actual_weight_sum += r[4]  # 只累加实际检测到的ROI权重
                if blob_width < 130:
                    # 只有宽度<200的色块才参与计算
                    centroid_sum += largest_blob.cx() * r[4]
                    actual_weight_sum += r[4]
                            # 检测十字路口：最下面的ROI区域（i=0，底部ROI）色块框较大时
                if i == 0:  # 底部ROI区域
                    # blob_rect = largest_blob.rect()
                    # blob_width = blob_rect[2]  # 获取色块宽度
                    # blob_height = blob_rect[3]  # 获取色块高度
#                    blob_area = blob_width * blob_height
                    # 当色块面积较大时，判断为十字路口
                    if blob_width > 400:  # 阈值可根据实际情况调整
                        crossroad_detected = True

        # 使用动态权重总和，避免缺失色块时的误差偏移
        if actual_weight_sum > 0:
            center_pos = centroid_sum / actual_weight_sum
        else:
            center_pos = IMAGE_WIDTH / 2  # 没有检测到任何色块时，默认居中

        deflection_angle = 0

        deflection_angle = -math.atan((center_pos - IMAGE_WIDTH / 2) / (IMAGE_HEIGHT / 2))
        deflection_angle = math.degrees(deflection_angle)

        # 扩大误差范围：从-50~50扩大到-100~100，用于PID控制
        deflection_angle = deflection_angle * 1.3  # 乘以2倍扩大范围
        deflection_angle = int(deflection_angle)
        if deflection_angle > 70:
            deflection_angle = 70
        if deflection_angle < -70:
            deflection_angle = -70
        angle_byte = deflection_angle + 70
#        print(f"Turn Angle:{deflection_angle}")

        # direction = angle_to_direction(deflection_angle)  # 你可以自己定义这个映射函数
        frame1 = bytes([0x01, angle_byte, 0x01 ^ angle_byte])
        if frame1_flag:
            uart0.write(frame1)

        # 检测是否找不到任何巡线色块，触发停车指令
        if actual_weight_sum == 0:  # 没有检测到任何色块
            frame0 = bytes([0x04, 0x55, 0x04 ^ 0x55])  # 停车指令帧格式
            frame0_flag = 1

        # 检测十字路口，发送十字路口指令
        if crossroad_detected:
            frame_crossroad = bytes([0x05, 0x66, 0x05 ^ 0x66])  # 十字路口指令帧格式
            uart0.write(frame_crossroad)

        if frame0_flag:
            uart0.write(frame0)
            frame0_flag = 0
        # 显示图像及角度
        line_osd_img.draw_string_advanced(2, 2, 20, str('%.1f' % deflection_angle), color=(255, 255, 255))
        Display.show_image(line_osd_img, 0, 0, Display.LAYER_OSD2)

        gc.collect()

def yolov8_det_thread():
    global sensor,osd_img,rgb888p_size,display_size,yolo_osd_img,frame2_flag,frame1_flag,frame3_flag
    kmodel_path= "/data/my_model/number_recognizaton/best.kmodel"
    labels = ["3", "4", "5", "6", "7", "8"]
    confidence_threshold = 0.65
    nms_threshold = 0.6

    #串口标志位
    reg_first = True
    reg_second = False
    reg_contine = False
    data = None
    # T_flag = False

    ob_det=ObjectDetectionApp(kmodel_path,labels=labels,model_input_size=[320,320],max_boxes_num=7,confidence_threshold=confidence_threshold,nms_threshold=nms_threshold,rgb888p_size=rgb888p_size,display_size=display_size,debug_mode=0)
    ob_det.config_preprocess()

    cls_current = None
    cls_first_time = 0
    cla_last = None
    cls_first = None

    left_count = 0
    right_count = 0
    stright_count = 0
    confirm_threshold = 5

    left_detect_history = []

    while True:
        diversion = 0
        if yolo_det_stop:
            break

        data = uart1.read()
        if data and data.decode().strip() == "gettarget":
            frame2_flag = 1
            reg_first = True
            reg_second = False
            print("上位机请求开始识别\r\n")

        img_2 = sensor.snapshot(chn = CAM_CHN_ID_2)
        img_np =img_2.to_numpy_ref()
        with lock:
            det_res = ob_det.run(img_np)
        if det_res:
            boxes, class_ids, confidences = det_res
            if len(boxes) == 2:
                reg_second = True

        if reg_first:
            if det_res:
                boxes, class_ids, confs = det_res
                for i in range(len(boxes)):
                    box = boxes[i]
                    x, y, w, h = box
                    cls_id = class_ids[i]
                    conf = confs[i]
                    # print(f"数字上边框像素点{y}")

                    if conf > 0.8:
                        cls_current = int(labels[int(cls_id)])
                        # print(f"当前类别: {cls_current}")
                        # 第一次赋值或与上次不同则重置计数
                        if cla_last is None or cla_last != cls_current:
                            cls_first_time = 1
                            cla_last = cls_current
                        else:
                            cls_first_time += 1

                        if cls_first_time >= 3 and frame2_flag == 1:
                            cls_first = cls_current
                            print(f"连续3次确认类别: {cls_first}")
                            frame2 = bytes([0x02, cls_first,0x02 ^ cls_first])

                            uart1.write(frame2)
                            frame2_flag = 0
                            frame1_flag = 1
                            frame3_flag = 0
                            reg_first = False
                            reg_second = True

        elif not reg_first and reg_second:
            if det_res:
                boxes, class_ids, confidences = det_res

                # print("开始判断方向")
                if len(boxes) == 2:
                    # print("此时数字有2个")
                    x1_tar = 0
                    x1_lr = 0
                    y1_tar = 0
                    y1_lr = 0
                    has_target = False

                    for box, cls_id, conf in zip(boxes, class_ids, confidences):
                        x, y, w, h = box
                        label = int(labels[int(cls_id)])

                        if conf > 0.75:
                            if label == cls_first:
                                x1_tar = x
                                y1_tar = y
                                has_target = True
                            else:
                                x1_lr = x
                                y1_lr = y

                    if has_target:
                        if x1_tar != 0 and x1_lr != 0:
                            if x1_tar < x1_lr :
                                left_count += 1
                                right_count = 0  # 清空右转统计
                                # print("turn left 1")
                            elif x1_tar > x1_lr :
                                right_count += 1
                                left_count = 0  # 清空左转统计
                    else:
                        stright_count += 1
                        left_count = 0
                        right_count = 0

                    if left_count >= confirm_threshold:
                        diversion = 0xf0  # turn left
                        print("turn left")
                        frame3_flag = 1
                        frame1_flag = 0
                        frame3 = bytes([0x03, diversion, 0x03 ^ diversion])
                    elif right_count >= confirm_threshold:
                        diversion = 0x0f  # turn right
                        print("turn right")
                        frame3_flag = 1
                        frame1_flag = 0
                        frame3 = bytes([0x03, diversion, 0x03 ^ diversion])
                    elif stright_count >= confirm_threshold:
                        diversion = 0xff  # turn right
                        print("go stright")
                        frame3_flag = 1
                        frame1_flag = 0
                        frame3 = bytes([0x03, diversion, 0x03 ^ diversion])

                    if frame3_flag:
                        uart1.write(frame3)
                        frame3_flag = 0
                        frame1_flag = 1

                elif len(boxes) >= 3:  # 处理3个或更多数字的情况
                    # 按X坐标从左到右排序所有检测到的数字
                    detections = sorted(
                        [(box, int(labels[int(cls_id)]), conf) for box, cls_id, conf in
                         zip(boxes, class_ids, confidences) if conf > 0.75],
                        key=lambda item: item[0][0]  # 按X坐标排序
                    )

                    if len(detections) >= 2:
                        # 取左边两个数字
                        left_two_detections = detections[:2]

                        # 检查左边两个数字中是否有目标数字
                        target_found_in_left = False
                        for box, label, conf in left_two_detections:
                            if label == cls_first:
                                target_found_in_left = True
                                break

                        # 根据目标数字位置决定转向
                        if target_found_in_left:
                            left_count += 1
                            right_count = 0
                            stright_count = 0
                            print(f"4数字模式：目标数字在左边两个中，左转")
                        else:
                            right_count += 1
                            left_count = 0
                            stright_count = 0
                            print(f"4数字模式：目标数字不在左边两个中，右转")

                        # 发送转向指令（使用相同的确认机制）
                        if left_count >= confirm_threshold:
                            diversion = 0xf0  # turn left
                            print("4数字确认左转")
                            frame3_flag = 1
                            frame1_flag = 0
                            frame3 = bytes([0x03, diversion, 0x03 ^ diversion])
                        elif right_count >= confirm_threshold:
                            diversion = 0x0f  # turn right
                            print("4数字确认右转")
                            frame3_flag = 1
                            frame1_flag = 0
                            frame3 = bytes([0x03, diversion, 0x03 ^ diversion])

                        if frame3_flag:
                            uart1.write(frame3)
                            frame3_flag = 0
                            frame1_flag = 1

        ob_det.draw_result(yolo_osd_img, det_res)
        Display.show_image(yolo_osd_img, 0, 0, Display.LAYER_OSD1)
        gc.collect()
    ob_det.deinit()


def media_init():
    global sensor,osd_img,rgb888p_size,display_size,line_osd_img,yolo_osd_img

    Display.init(Display.ST7701, width = DISPLAY_WIDTH, height = DISPLAY_HEIGHT, to_ide =False, osd_num=3)
    sensor = Sensor()  # 修复：移除fps参数
    sensor.reset()
    sensor.set_vflip(True)
    sensor.set_hmirror(True)  # 在哪个位置 进行垂直翻转？
    sensor.set_framesize(w=800, h=480, chn=CAM_CHN_ID_0)
    sensor.set_pixformat(Sensor.YUV420SP, chn=CAM_CHN_ID_0)  # 修复：添加chn参数
    sensor.set_framesize(w =800, h = 480, chn=CAM_CHN_ID_1)
    sensor.set_pixformat(Sensor.RGB565, chn=CAM_CHN_ID_1)
    sensor.set_framesize(w = rgb888p_size[0], h = rgb888p_size[1], chn=CAM_CHN_ID_2)
    sensor.set_pixformat(Sensor.RGBP888, chn=CAM_CHN_ID_2)

    sensor_bind_info = sensor.bind_info(x = 0, y = 0, chn = CAM_CHN_ID_0)
    Display.bind_layer(**sensor_bind_info, layer = Display.LAYER_VIDEO1)
    line_osd_img = image.Image(display_size[0], display_size[1], image.ARGB8888)
    yolo_osd_img = image.Image(display_size[0], display_size[1], image.ARGB8888)
    MediaManager.init()
    sensor.run()

def media_deinit():
    global sensor
    os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
    sensor.stop()
    Display.deinit()
    time.sleep_ms(50)
    MediaManager.deinit()

if __name__ == "__main__":
    media_init()
    _thread.start_new_thread(yolov8_det_thread,())
    _thread.start_new_thread(line_following_thread,())
    try:
        while True:
            time.sleep_ms(100)
    except BaseException as e:
        import sys
        sys.print_exception(e)
        yolo_det_stop=True
        line_flw_stop=True

    time.sleep(1)
    media_deinit()
    gc.collect()
