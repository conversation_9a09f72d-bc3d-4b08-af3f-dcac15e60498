
import time
import os
from media.sensor import *
from media.display import *
from media.media import *
from time import ticks_ms

from machine import FPIOA
from machine import Pin
from machine import UART
from machine import TOUCH
import  cv_lite
sensor = None
import math

# --- MODIFICATION START ---
# 将竞赛流程设置为1，以直接测试矩形识别
contest_flow = 0

sensor = None
uart2 = None
osd_img = None
clock = None
rgb888_size = [800,480]
display_size =[800,480]
image_shape = [480,800]
draw_img = None
# --- MODIFICATION END ---

DISPLAY_WIDTH = 800
DISPLAY_HEIGHT = 480
roi = (233, 0, 319, 480)

# Variables for smoothing
prev_blob_data = {'x': -1, 'y': -1, 'w': -1, 'h': -1}
alpha = 1 # Smoothing factor

draw_img_stop = None
plaser_x = 0
plaser_y = 0

rectangle_detected = True
triangle_detected = False

current_target_index = 0
target_points = []
arrival_threshold = 10
def media_init():
    global sensor,osd_img,rgb888_size,display_size,draw_img,uart2,uart3,clock

    fpioa = FPIOA()
    fpioa.set_function(11, FPIOA.UART2_TXD)
    fpioa.set_function(12, FPIOA.UART2_RXD)
    uart2 = UART(UART.UART2, 115200)

    Display.init(Display.ST7701, width = 800, height =480, to_ide =True, osd_num=2)
    sensor = Sensor(fps=30)
    sensor.reset()
    sensor.set_framesize(w =800, h = 480, chn=CAM_CHN_ID_0)
    sensor.set_pixformat(Sensor.RGB565, chn=CAM_CHN_ID_0)

    MediaManager.init()
    sensor.run()
    clock = time.clock()

def media_deinit():
    global sensor
    os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
    sensor.stop()
    Display.deinit()
    MediaManager.deinit()

class ThresholdAdjuster:
    """脱机阈值调节器类"""

    def __init__(self, sensor, tp, threshold_dict, cut_roi, show_img_func):
        """
        初始化阈值调节器
        Args:
            sensor: 摄像头对象
            tp: 触摸屏对象
            threshold_dict: 阈值字典
            cut_roi: 图像裁剪区域
            show_img_func: 显示图像函数
        """
        self.sensor = sensor
        self.tp = tp
        self.threshold_dict = threshold_dict
        self.cut_roi = cut_roi
        self.show_img_func = show_img_func

        # GUI配置
        self.button_color = (150, 150, 150)
        self.text_color = (0, 0, 0)
        self.screen_width = 800
        self.screen_height = 480

        # 阈值调节状态
        self.threshold_mode = 'rect'
        self.threshold_current = [0, 255, 0, 255, 0, 255]


    def clear_thresholds(self):
        """清空当前阈值（仅在需要时使用）"""
        for key in self.threshold_dict.keys():
            self.threshold_dict[key] = []

    def load_existing_threshold(self):
        """加载现有阈值到当前编辑器（固定加载第一个阈值）"""
        if self.threshold_mode in self.threshold_dict:
            thresholds = self.threshold_dict[self.threshold_mode]
            if len(thresholds) > 0:
                threshold = thresholds[0]

                if self.threshold_mode in ['purple_point1', 'purple_point2']:
                    # LAB阈值直接使用原始值，不进行转换
                    self.threshold_current = list(threshold)
                    color_name = "红点1" if self.threshold_mode == 'purple_point1' else "红点2"
                    print(f"加载{color_name}阈值:")
                    print(f"  原始LAB值: {threshold}")
                    print(f"  显示LAB值: {self.threshold_current}")
                    print(f"  l_min:{self.threshold_current[0]} l_max:{self.threshold_current[1]}")
                    print(f"  a_min:{self.threshold_current[2]} a_max:{self.threshold_current[3]}")
                    print(f"  b_min:{self.threshold_current[4]} b_max:{self.threshold_current[5]}")
                elif self.threshold_mode == 'rect':
                    # 灰度阈值扩展到6个参数
                    self.threshold_current = list(threshold) + [0, 255, 0, 255]
                    print(f"加载矩形阈值: {threshold} -> {self.threshold_current}")

                return True

        # 如果没有现有阈值，使用默认值
        if self.threshold_mode in ['purple_point1', 'purple_point2']:
            self.threshold_current = [-128, 127, -128, 127, -128, 127]
        else:
            self.threshold_current = [0, 255, 0, 255, 0, 255]
        print("没有现有阈值，使用默认值")
        return False

    def debug_threshold_data(self):
        """调试阈值数据"""
        print("=== 阈值数据调试 ===")
        for mode, thresholds in self.threshold_dict.items():
            print(f"{mode}")
            for i, threshold in enumerate(thresholds):
                print(f"  第{i+1}个阈值: {threshold}")
                if mode in ['purple_point1', 'purple_point2']:
                    print(f"    LAB值: {threshold}")
                    print(f"    l_min:{threshold[0]} l_max:{threshold[1]} a_min:{threshold[2]} a_max:{threshold[3]} b_min:{threshold[4]} b_max:{threshold[5]}")
        print("==================")

    def create_gui_canvas(self):
        """创建GUI画布"""
        img = image.Image(self.screen_width, self.screen_height, image.RGB565)
        img.draw_rectangle(0, 0, self.screen_width, self.screen_height,
                          color=(255, 255, 255), thickness=2, fill=True)

        # 绘制功能按钮
        self._draw_function_buttons(img)
        # 绘制滑块控制按钮
        self._draw_slider_buttons(img)

        return img

    def _draw_function_buttons(self, img):
        """绘制功能按钮"""
        # 返回按钮
        img.draw_rectangle(0, 0, 160, 40, color=self.button_color, thickness=2, fill=True)
        img.draw_string_advanced(50, 0, 30, "返回", color=self.text_color)

        # 切换模式按钮
        img.draw_rectangle(self.screen_width-160, 0, 160, 40,
                          color=self.button_color, thickness=2, fill=True)
        img.draw_string_advanced(self.screen_width-160+40, 0, 30, "切换模式", color=self.text_color)

        # 归位按钮
        img.draw_rectangle(0, self.screen_height-40, 160, 40,
                          color=self.button_color, thickness=2, fill=True)
        img.draw_string_advanced(50, self.screen_height-40, 30, "归位", color=self.text_color)

        # 保存按钮
        img.draw_rectangle(self.screen_width-160, self.screen_height-40, 160, 40,
                          color=self.button_color, thickness=2, fill=True)
        img.draw_string_advanced(self.screen_width-160+50, self.screen_height-40, 30, "保存", color=self.text_color)

    def _draw_slider_buttons(self, img):
        """绘制滑块控制按钮"""
        for j in [0, self.screen_width - 160]:
            for i in range(60, 420, 60):
                img.draw_rectangle(j, i, 160, 40, color=self.button_color, thickness=2, fill=True)

    def identify_button(self, x, y):
        """识别按下的按钮"""
        if x < 160:  # 左侧按钮
            if y < 40:
                return "return"
            if y > self.screen_height - 40:
                return "reset"
            if y > 60 and (y - 60) % 60 < 40:
                return str((y - 60) // 60)  # 滑块0-5（减少）
        elif x > self.screen_width - 160:  # 右侧按钮
            if y < 40:
                return "change_mode"
            if y > self.screen_height - 40:
                return "save"
            if y > 60 and (y - 60) % 60 < 40:
                return str((y - 60) // 60 + 6)  # 滑块6-11（增加）

        return None

    def process_image_preview(self, img):
        """处理图像预览"""
        # 获取图像
        img_preview = self.sensor.snapshot()
        img_preview = img_preview.copy(roi=self.cut_roi)

        # 根据阈值模式处理图像
        if self.threshold_mode == 'rect':
            # 灰度阈值处理
            img_preview = img_preview.to_grayscale()
            threshold_min = max(0, min(255, self.threshold_current[0]))
            threshold_max = max(threshold_min, min(255, self.threshold_current[1]))
            img_preview = img_preview.binary([(threshold_min, threshold_max)])
            img_preview = img_preview.to_rgb565()

        elif self.threshold_mode in ['purple_point1', 'purple_point2']:
            # LAB阈值处理
            lab_threshold = [max(-128, min(127, val)) for val in self.threshold_current]
            img_preview = img_preview.binary([lab_threshold])
            img_preview = img_preview.to_rgb565()

        # 将预览图像绘制到画布中央
        x_pos = (self.screen_width - img_preview.width()) // 2
        y_pos = (self.screen_height - img_preview.height()) // 2
        img.draw_image(img_preview, x_pos, y_pos)

        # 显示当前阈值参数
        self._draw_threshold_info(img)

    def _draw_threshold_info(self, img):
        """绘制阈值信息"""
        # 显示当前模式
        mode_text = self.threshold_mode
        img.draw_string_advanced(170, 10, 25, mode_text, color=(0, 0, 255))

        # 显示当前参数值 - 使用LAB色彩空间标识
        param_labels = ['l_min', 'l_max', 'a_min', 'a_max', 'b_min', 'b_max']

        for i in range(6):
            value = self.threshold_current[i]
            x_pos = 170 + (i % 3) * 90
            y_pos = 35 + (i // 3) * 25

            # 根据阈值模式选择标签
            if self.threshold_mode in ['purple_point1', 'purple_point2']:
                label = param_labels[i]
            else:
                label = str(i)  # rect模式仍使用数字标号

            img.draw_string_advanced(x_pos, y_pos, 16, f"{label}:{value}", color=(0, 0, 255))

    def show_message(self, img, message):
        """显示状态消息"""
        temp_img = img.copy()
        temp_img.draw_rectangle(200, 200, 300, 40, color=self.button_color, thickness=2, fill=True)
        temp_img.draw_string_advanced(200, 200, 30, message, color=self.text_color)
        self._display_image(temp_img)
        time.sleep_ms(50) #原1500

    def _display_image(self, img):
        """图像显示函数"""
        if img.height() > 480 or img.width() > 800:
            scale = max(img.height() // 480, img.width() // 800) + 1
            img.midpoint_pool(scale, scale)
        img.compress_for_ide()
        Display.show_image(img, x=(800-img.width())//2, y=(480-img.height())//2)

    def handle_button_action(self, button, img):
        """处理按钮动作"""
        if button == "return":
            print("点击返回按钮")
            return True  # 退出调整模式

        elif button == "change_mode":
            # 切换阈值模式
            threshold_modes = list(self.threshold_dict.keys())
            current_index = threshold_modes.index(self.threshold_mode)
            self.threshold_mode = threshold_modes[(current_index + 1) % len(threshold_modes)]
            self.load_existing_threshold()  # 加载新模式的阈值
            print(f"切换到模式: {self.threshold_mode}")
            self.show_message(img, f"模式:{self.threshold_mode}")

        elif button == "reset":
            # 重置滑块到原始值
            self.load_existing_threshold()  # 重新加载原始值
            print("重置到原始值")
            self.show_message(img, "重置原始值")

        elif button == "save":
            # 保存当前阈值（固定更新第一个阈值）
            if self.threshold_mode in self.threshold_dict:
                thresholds = self.threshold_dict[self.threshold_mode]
                if len(thresholds) > 0:
                    if self.threshold_mode in ['purple_point1', 'purple_point2']:
                        thresholds[0] = tuple(self.threshold_current)
                    elif self.threshold_mode == 'rect':
                        thresholds[0] = self.threshold_current[:2]
                    print(f"更新第一个阈值: {self.threshold_current}")
                    self.show_message(img, "更新成功")
                else:
                    self.show_message(img, "无阈值可更新")

        elif button and button.isdigit():
            # 滑块调整
            button_num = int(button)
            param_index = button_num % 6
            old_value = self.threshold_current[param_index]

            # 根据阈值模式设置调整范围
            if self.threshold_mode in ['purple_point1', 'purple_point2']:
                min_val, max_val = -128, 127
            else:
                min_val, max_val = 0, 255

            if button_num >= 6:  # 右侧按钮（增加）
                self.threshold_current[param_index] = min(max_val, self.threshold_current[param_index] + 2)
            else:  # 左侧按钮（减少）
                self.threshold_current[param_index] = max(min_val, self.threshold_current[param_index] - 2)

            new_value = self.threshold_current[param_index]

            # 根据阈值模式显示不同的参数名称
            if self.threshold_mode in ['purple_point1', 'purple_point2']:
                param_names = ['l_min', 'l_max', 'a_min', 'a_max', 'b_min', 'b_max']
                param_name = param_names[param_index]
                print(f"调整参数{param_name}: {old_value} -> {new_value}")
            else:
                print(f"调整参数{param_index}: {old_value} -> {new_value}")

        return False  # 继续调整模式

    def run_adjustment_mode(self):
        """运行阈值调整模式"""
        print("进入阈值调整模式")

        # 调试阈值数据
        self.debug_threshold_data()

        # 加载现有阈值进行微调，而不是清空
        self.load_existing_threshold()
        print(f"当前模式: {self.threshold_mode}")
        print(f"当前阈值: {self.threshold_current}")

        # 添加退出标志
        self.is_running = True
        touch_debounce = 0  # 触摸防抖计数器

        try:
            while self.is_running:
                # 重新创建GUI画布（每次循环都重新创建以确保界面更新）
                img = self.create_gui_canvas()

                # 处理图像预览
                try:
                    self.process_image_preview(img)
                except Exception as e:
                    print(f"图像预览错误: {e}")
                    # 如果图像预览失败，显示错误信息
                    img.draw_string_advanced(200, 240, 30, "图像错误", color=(255, 0, 0))

                # 检测触摸输入（添加防抖）
                if touch_debounce > 0:
                    touch_debounce -= 1
                else:
                    points = self.tp.read()
                    if len(points) > 0:
                        button = self.identify_button(points[0].x, points[0].y)
                        if button:
                            print(f"按钮点击: {button}")
                            should_exit = self.handle_button_action(button, img)
                            if should_exit:
                                self.is_running = False
                                break
                            touch_debounce = 10  # 设置防抖延时

                # 显示界面
                self._display_image(img)

                # 添加小延时避免CPU占用过高
                # time.sleep_ms(50)

        finally:
            print("退出阈值调整模式")

        # 返回修改后的阈值字典
        return self.threshold_dict

def find_rectangle(img):
    """自动检测矩形框"""
    global recognition_count,rectangle_detected,image_shape

    # 进行矩形识别
    # binary_img = img.binary([(6, 22, -10, 8, -17, 7)], invert=True, zero=True)
    # binary_img.gaussian(2)
    # rects = binary_img.find_rects(roi=roi, threshold=1000)
    try:
        canny_thresh1 = 50  # Canny 边缘检测低阈值 / Canny edge low threshold
        canny_thresh2 = 150  # Canny 边缘检测高阈值 / Canny edge high threshold
        approx_epsilon = 0.04  # 多边形拟合精度（比例） / Polygon approximation precision (ratio)
        area_min_ratio = 0.001  # 最小面积比例（0~1） / Minimum area ratio (0~1)
        max_angle_cos = 0.5  # 最大角余弦（值越小越接近矩形） / Max cosine of angle (smaller closer to rectangle)
        gaussian_blur_size = 5  # 高斯模糊核大小（奇数） / Gaussian blur kernel size (odd number)
        img_rec = img.copy()
        img_rec = img_rec.to_rgb888(x_scale=1.0, y_scale=1.0, roi=None, rgb_channel=-1, alpha=256, color_palette=None,
                        alpha_palette=None, hint=0, alloc=ALLOC_MMZ, cache=True, phyaddr=0, virtaddr=0, poolid=0)
        img_np = img_rec.to_numpy_ref()
        rects = cv_lite.rgb888_find_rectangles(
            image_shape, img_np,
            canny_thresh1, canny_thresh2,
            approx_epsilon,
            area_min_ratio,
            max_angle_cos,
            gaussian_blur_size
        )
        for i in range(0, len(rects), 4):
            x = rects[i]
            y = rects[i + 1]
            w = rects[i + 2]
            h = rects[i + 3]
            img.draw_rectangle(x, y, w, h, color=(255, 0, 0), thickness=2)
        print(f"正在进行矩形检测,矩形检测数量是{len(rects)}")
    except Exception as e:
        print("矩形检测失败")
        return []
    if len(rects):
        try:
            corners = []
            Min_area = 0
            Max_area = 10000
            rectangles = []
            rectangle_choose = []
            for i in range(0, len(rects), 4):
                if i + 3 < len(rects):
                    x, y, w, h = rects[i], rects[i+1], rects[i+2], rects[i+3]
                    area = w * h
                    corners = [
                        (x, y),  # 左上
                        (x + w, y),  # 右上
                        (x + w, y + h),  # 右下
                        (x, y + h),  # 左下
                        ((2 * x + w) // 2, (2 * y + h) // 2) # 中心点
                    ]
                    rectangles.append((corners, area))

            for i in range(len(rectangles)):
                if Min_area <= rectangles[i][1] <= Max_area:
                    rectangle_choose = rectangles[i]
                    print(f"当前的面积是{rectangles[i][1]}")
                    print(f"当前矩形坐标是{rectangles[i][0]}")
                    rectangle_detected = True
            return rectangle_choose
        except Exception as e:
            print(f"矩形处理异常: {e}")
            return []

def draw_triangle(rectangle_choose):
    global triangle_detected
    if rectangle_choose is None:
        return
    try:
        corners = rectangle_choose[0]
        # 获取矩形信息
        center_x, center_y = corners[4]  # 中心点
        left_top = corners[0]  # 左上角
        right_top = corners[1]  # 右上角

        # 计算矩形宽度
        rect_width = right_top[0] - left_top[0]

        # 计算正三角形边长（矩形宽度的1/3）
        triangle_side = rect_width // 2

        # 计算正三角形的高度
        triangle_height = int(triangle_side * 0.866)  # sqrt(3)/2 ≈ 0.866

        # 计算正三角形的三个顶点（垂线垂直于水平面，即顶点在上方）
        top_vertex = (center_x, center_y - triangle_height // 2)  # 顶点
        bottom_left = (center_x - triangle_side // 2, center_y + triangle_height // 2)  # 左下角
        bottom_right = (center_x + triangle_side // 2, center_y + triangle_height // 2)  # 右下角

        print(f"绘制正三角形: 中心({center_x},{center_y}), 边长{triangle_side},顶点{top_vertex}，左下角{bottom_left }，右下角{bottom_right}")
        triangle = (top_vertex,bottom_left,bottom_right)
        triangle_detected = True
        return triangle
    except (IndexError, TypeError) as e:
        print(f"绘制三角形失败: {e}")
        return None
def find_blobs(img,thresh):
        global prev_blob_data,plaser_x,plaser_y
        #寻找色块，参数依次为颜色阈值, 是否反转，roi, x_stride, y_stride, pixels_threshold, margin(是否合并)
        purple_blobs = img.find_blobs(thresh, False, \
                                   roi=roi, x_stride=1, y_stride=1, \
                                   pixels_threshold=0, margin=True)

        if purple_blobs:
            current_blob = purple_blobs[0]
            if prev_blob_data['x'] == -1:  # First detection
                smoothed_x = current_blob.x()
                smoothed_y = current_blob.y()
                smoothed_w = current_blob.w()
                smoothed_h = current_blob.h()
            else:
                smoothed_x = int(alpha * current_blob.x() + (1 - alpha) * prev_blob_data['x'])
                smoothed_y = int(alpha * current_blob.y() + (1 - alpha) * prev_blob_data['y'])
                smoothed_w = int(alpha * current_blob.w() + (1 - alpha) * prev_blob_data['w'])
                smoothed_h = int(alpha * current_blob.h() + (1 - alpha) * prev_blob_data['h'])

            # Update previous blob data
            prev_blob_data['x'] = smoothed_x
            prev_blob_data['y'] = smoothed_y
            prev_blob_data['w'] = smoothed_w
            prev_blob_data['h'] = smoothed_h

            #draw_img.draw_circle(smoothed_x,smoothed_y, 3, color=(0, 255, 0), thickness=2, fill=False)
            purple_centerX = smoothed_x + smoothed_w / 2.0
            purple_centerY = smoothed_y + smoothed_h / 2.0

            # 更新激光点坐标
            plaser_x = int(purple_centerX)
            plaser_y = int(purple_centerY)

        else:
            prev_blob_data = {'x': -1, 'y': -1, 'w': -1, 'h': -1}

        return plaser_x,plaser_y
def generate_triangle_points(triangle):
    """生成三角形30个目标点（每边10等分）"""
    global target_points
    top_vertex, bottom_left, bottom_right = triangle

    for i in range(10):
        t = i / 10  # 0到1的比例
        x = int(top_vertex[0] + t * (bottom_left[0] - top_vertex[0]))
        y = int(top_vertex[1] + t * (bottom_left[1] - top_vertex[1]))
        target_points.append((x, y))

    for i in range(0, 10):
        t = i / 10
        x = int(bottom_left[0] + t * (bottom_right[0] - bottom_left[0]))
        y = int(bottom_left[1] + t * (bottom_right[1] - bottom_left[1]))
        target_points.append((x, y))

    for i in range(0, 10):
        t = i / 10
        x = int(bottom_right[0] + t * (top_vertex[0] - bottom_right[0]))
        y = int(bottom_right[1] + t * (top_vertex[1] - bottom_right[1]))
        target_points.append((x, y))

    return target_points
def purple_motion(laser_x, laser_y, triangle):
    """紫色激光笔运动控制"""
    global current_target_index, target_points, arrival_threshold,triangle_detected
    
    # 初始化目标点
    if not target_points:
        target_points = generate_triangle_points(triangle)
        current_target_index = 0
    
    # 获取当前目标点
    current_target = target_points[current_target_index]
    # print(f"当前目标点{current_target}")
    
    # 计算距离误差
    error_x = current_target[0] - laser_x
    error_y = current_target[1] - laser_y
    distance = math.sqrt(error_x**2 + error_y**2)

    # print(f"当前距离为{distance}")
    # 判断是否到达目标点
    if distance <= arrival_threshold:  # 例如阈值为10像素
        current_target_index = (current_target_index + 1) % len(target_points)
        print(f"到达目标点{current_target_index-1}，切换到目标点{current_target_index}")
    
    # 发送误差给下位机
    #send_error_to_controller(error_x, error_y, distance)
    error_send(error_x, error_y)
    print(f"紫色激光点{laser_x},{laser_y},当前目标点{current_target[0]},{current_target[1]}")
    return error_x, error_y, current_target

def error_send(dx,dy):
    global uart2
    if dx >= 0:
        dx_sign = 0x00  # 正数符号位
    else:
        dx_sign = 0x01  # 负数符号位
        dx = abs(dx)  # 取绝对值
    # 限制数据范围到0-255
    dx_data = min(abs(dx), 255)
    # 处理dy数据
    if dy >= 0:
        dy_sign = 0x00  # 正数符号位
    else:
        dy_sign = 0x01  # 负数符号位
        dy = abs(dy)  # 取绝对值
    # 限制数据范围到0-255
    dy_data = min(abs(dy), 255)
    # 构建帧数据 - 逐字节发送，
    frame_data = [0x22, dx_sign, dx_data, dy_sign, dy_data, 0x33]
    # 调试信息
    for byte_val in frame_data:
        uart2.write(bytes([byte_val]))
        print(
            f"串口2发送差值帧: dx({dx}) dy({dy})) 帧:{[hex(b) for b in frame_data]}")

try:
    count = 0
    flag = 0
    media_init()
    print("系统启动，开始自动检测矩形框...")
    touch_counter = 0

    # 触摸屏初始化
    tp = TOUCH(0)

    threshold_dict = {
        'rect': [(59, 246)],
        'purple_point1': [(41, 63, 5, 127, -107, -3)],      # 第1个紫点阈值
        'purple_point2': [(14, 67, 7, 127, -112, -3)],      # 第2个紫点阈值
        'red_point3': [(30, 80, 20, 127, -20, 40)],         # 第3个红点阈值
        'red_point4': [(25, 75, 15, 127, -25, 35)]          # 第4个红点阈值
    }
    corners = []
    triangle = None
    top_vertex = []
    bottom_left = []
    bottom_right =[]
    # (62, 75, 5, 50, -54, -16), (14, 67, 7, 127, -112, -3)
    def show_img_2_screen():
        global img
        if(img.height()>480 or img.width() > 800):
            scale = max(img.height() // 480, img.width() // 800) + 1
            img.midpoint_pool(scale, scale)
        img.compress_for_ide()
        Display.show_image(img, x=(800-img.width())//2, y=(480-img.height())//2)

    while True:
        clock.tick()
        os.exitpoint()
        if flag == 0:
            img = sensor.snapshot(chn=CAM_CHN_ID_0)
            # if not rectangle_detected:
            #     rectangle_choose = find_rectangle(img)
            #     if rectangle_choose:
            #         corners = rectangle_choose[0][:4]
            if not triangle_detected and rectangle_detected:
                rectangles = []
                corners = [
                    (200, 100),  # 左上
                    (600, 100),  # 右上
                    (600, 300),  # 右下
                    (200, 300),  # 左下
                    (400, 150)  # 中心点
                ]
                area = 2000
                rectangle_choose = (corners,area)
                triangle = draw_triangle(rectangle_choose)
                top_vertex = triangle[0]
                bottom_left = triangle[1]
                bottom_right = triangle[2]
            if(contest_flow == 0):
                threshold_purple = list(threshold_dict['purple_point1']) + list(threshold_dict['purple_point2'])
                plaser_x,plaser_y= find_blobs(img,threshold_purple)
                if plaser_x > 0 and plaser_y > 0 and triangle:
                    error_x, error_y, current_target = purple_motion(plaser_x, plaser_y, triangle)
                    img.draw_circle(current_target[0], current_target[1], 5, color=(255, 0, 0), thickness=4, fill=True)
                    #draw_img.draw_line(plaser_x, plaser_y, target_point[0], target_point[1], color=(0, 255, 255), thickness=1)
                    img.draw_circle(plaser_x,plaser_y,5,color = (168,85,211),thickness = 4,fill = True)
            if corners:
                for i in range(4):
                    x1, y1 = corners[i]
                    x2, y2 = corners[(i+1) % 4]
                    img.draw_line(x1, y1, x2, y2, color=(255, 0, 0), thickness=2)
            if triangle:
                img.draw_line(top_vertex[0], top_vertex[1], bottom_left[0], bottom_left[1],
                              color=(255, 255, 0), thickness=1)  # 黄色，更粗
                img.draw_line(bottom_left[0], bottom_left[1], bottom_right[0], bottom_right[1],
                              color=(255, 255, 0), thickness=1)
                img.draw_line(bottom_right[0], bottom_right[1], top_vertex[0], top_vertex[1],
                              color=(255, 255, 0), thickness=1)
            if target_points:
                for point in target_points:
                    x,y = point
                    img.draw_circle(x, y, 2, color=(192, 192, 192), thickness=2, fill=True)
            # 绘制ROI区域
            img.draw_rectangle(roi, color=(0, 0, 255), thickness=1, fill=False)
            img.draw_string_advanced(10,10,20,f"fps:.{int(clock.fps())}",color = (255,0,0))
            Display.show_image(img)
        elif flag == 2:
            try:
                # 创建阈值调节器实例
                adjuster = ThresholdAdjuster(sensor, tp, threshold_dict, roi, show_img_2_screen)
                # 运行阈值调整模式
                adjuster.run_adjustment_mode()

                print("阈值调整完成")
                print(f"最终阈值: {threshold_dict}")

            except Exception as e:
                print(f"阈值调整模式错误: {e}")
            finally:
                # 调整完成后返回正常模式
                flag = 0
                time.sleep_ms(300)

        points = tp.read()
        if len(points) > 0:
            touch_counter += 1
            if touch_counter > 20:
                flag = 2
            print(points[0].x)
        else:
            touch_counter -= 2
            touch_counter = max(0, touch_counter)

except KeyboardInterrupt as e:
    print("用户停止: ", e)
except BaseException as e:
    print(f"异常: {e}")
    import sys
    sys.print_exception(e)
    draw_img_stop = True
finally:
    media_deinit()
    time.sleep_ms(100)

