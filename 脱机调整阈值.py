import time
import os
import sys
import image

from media.sensor import *
from media.display import *
from media.media import *
from time import ticks_ms
from machine import FPIOA
from machine import Pin
from machine import PWM
from machine import Timer
from machine import TOUCH

# 阈值调节器类
class ThresholdAdjuster:
    """脱机阈值调节器类"""

    def __init__(self, sensor, tp, threshold_dict, cut_roi, show_img_func):
        """
        初始化阈值调节器
        Args:
            sensor: 摄像头对象
            tp: 触摸屏对象
            threshold_dict: 阈值字典
            cut_roi: 图像裁剪区域
            show_img_func: 显示图像函数
        """
        self.sensor = sensor
        self.tp = tp
        self.threshold_dict = threshold_dict
        self.cut_roi = cut_roi
        self.show_img_func = show_img_func

        # GUI配置
        self.button_color = (150, 150, 150)
        self.text_color = (0, 0, 0)
        self.screen_width = 800
        self.screen_height = 480

        # 阈值调节状态
        self.threshold_mode = 'rect'
        self.threshold_current = [0, 255, 0, 255, 0, 255]


    def clear_thresholds(self):
        """清空当前阈值（仅在需要时使用）"""
        for key in self.threshold_dict.keys():
            self.threshold_dict[key] = []

    def load_existing_threshold(self):
        """加载现有阈值到当前编辑器（固定加载第一个阈值）"""
        if self.threshold_mode in self.threshold_dict:
            thresholds = self.threshold_dict[self.threshold_mode]
            if len(thresholds) > 0:
                threshold = thresholds[0]  # 固定加载第一个阈值

                if self.threshold_mode in ['red_point1', 'red_point2']:
                    # LAB阈值直接使用原始值，不进行转换
                    self.threshold_current = list(threshold)
                    color_name = "红点1" if self.threshold_mode == 'red_point1' else "红点2"
                    print(f"加载{color_name}阈值:")
                    print(f"  原始LAB值: {threshold}")
                    print(f"  显示LAB值: {self.threshold_current}")
                    print(f"  l_min:{self.threshold_current[0]} l_max:{self.threshold_current[1]}")
                    print(f"  a_min:{self.threshold_current[2]} a_max:{self.threshold_current[3]}")
                    print(f"  b_min:{self.threshold_current[4]} b_max:{self.threshold_current[5]}")
                elif self.threshold_mode == 'rect':
                    # 灰度阈值扩展到6个参数
                    self.threshold_current = list(threshold) + [0, 255, 0, 255]
                    print(f"加载矩形阈值: {threshold} -> {self.threshold_current}")

                return True

        # 如果没有现有阈值，使用默认值
        if self.threshold_mode in ['red_point1', 'red_point2']:
            self.threshold_current = [-128, 127, -128, 127, -128, 127]
        else:
            self.threshold_current = [0, 255, 0, 255, 0, 255]
        print("没有现有阈值，使用默认值")
        return False

    def debug_threshold_data(self):
        """调试阈值数据"""
        print("=== 阈值数据调试 ===")
        for mode, thresholds in self.threshold_dict.items():
            print(f"{mode}")
            for i, threshold in enumerate(thresholds):
                print(f"  第{i+1}个阈值: {threshold}")
                if mode in ['red_point1', 'red_point2']:
                    print(f"    LAB值: {threshold}")
                    print(f"    l_min:{threshold[0]} l_max:{threshold[1]} a_min:{threshold[2]} a_max:{threshold[3]} b_min:{threshold[4]} b_max:{threshold[5]}")
        print("==================")

    def create_gui_canvas(self):
        """创建GUI画布"""
        img = image.Image(self.screen_width, self.screen_height, image.RGB565)
        img.draw_rectangle(0, 0, self.screen_width, self.screen_height,
                          color=(255, 255, 255), thickness=2, fill=True)

        # 绘制功能按钮
        self._draw_function_buttons(img)

        # 绘制滑块控制按钮
        self._draw_slider_buttons(img)

        return img

    def _draw_function_buttons(self, img):
        """绘制功能按钮"""
        # 返回按钮
        img.draw_rectangle(0, 0, 160, 40, color=self.button_color, thickness=2, fill=True)
        img.draw_string_advanced(50, 0, 30, "返回", color=self.text_color)

        # 切换模式按钮
        img.draw_rectangle(self.screen_width-160, 0, 160, 40,
                          color=self.button_color, thickness=2, fill=True)
        img.draw_string_advanced(self.screen_width-160+40, 0, 30, "切换模式", color=self.text_color)

        # 归位按钮
        img.draw_rectangle(0, self.screen_height-40, 160, 40,
                          color=self.button_color, thickness=2, fill=True)
        img.draw_string_advanced(50, self.screen_height-40, 30, "归位", color=self.text_color)

        # 保存按钮
        img.draw_rectangle(self.screen_width-160, self.screen_height-40, 160, 40,
                          color=self.button_color, thickness=2, fill=True)
        img.draw_string_advanced(self.screen_width-160+50, self.screen_height-40, 30, "保存", color=self.text_color)

    def _draw_slider_buttons(self, img):
        """绘制滑块控制按钮"""
        for j in [0, self.screen_width - 160]:
            for i in range(60, 420, 60):
                img.draw_rectangle(j, i, 160, 40, color=self.button_color, thickness=2, fill=True)

    def identify_button(self, x, y):
        """识别按下的按钮"""
        if x < 160:  # 左侧按钮
            if y < 40:
                return "return"
            if y > self.screen_height - 40:
                return "reset"
            if y > 60 and (y - 60) % 60 < 40:
                return str((y - 60) // 60)  # 滑块0-5（减少）
        elif x > self.screen_width - 160:  # 右侧按钮
            if y < 40:
                return "change_mode"
            if y > self.screen_height - 40:
                return "save"
            if y > 60 and (y - 60) % 60 < 40:
                return str((y - 60) // 60 + 6)  # 滑块6-11（增加）

        return None

    def process_image_preview(self, img):
        """处理图像预览"""
        # 获取图像
        img_preview = self.sensor.snapshot()
        img_preview = img_preview.copy(roi=self.cut_roi)

        # 根据阈值模式处理图像
        if self.threshold_mode == 'rect':
            # 灰度阈值处理
            img_preview = img_preview.to_grayscale()
            threshold_min = max(0, min(255, self.threshold_current[0]))
            threshold_max = max(threshold_min, min(255, self.threshold_current[1]))
            img_preview = img_preview.binary([(threshold_min, threshold_max)])
            img_preview = img_preview.to_rgb565()

        elif self.threshold_mode in ['red_point1', 'red_point2']:
            # LAB阈值处理
            lab_threshold = [max(-128, min(127, val)) for val in self.threshold_current]
            img_preview = img_preview.binary([lab_threshold])
            img_preview = img_preview.to_rgb565()

        # 将预览图像绘制到画布中央
        x_pos = (self.screen_width - img_preview.width()) // 2
        y_pos = (self.screen_height - img_preview.height()) // 2
        img.draw_image(img_preview, x_pos, y_pos)

        # 显示当前阈值参数
        self._draw_threshold_info(img)

    def _draw_threshold_info(self, img):
        """绘制阈值信息"""
        # 显示当前模式
        mode_text = self.threshold_mode
        img.draw_string_advanced(170, 10, 25, mode_text, color=(0, 0, 255))

        # 显示当前参数值 - 使用LAB色彩空间标识
        param_labels = ['l_min', 'l_max', 'a_min', 'a_max', 'b_min', 'b_max']

        for i in range(6):
            value = self.threshold_current[i]
            x_pos = 170 + (i % 3) * 90
            y_pos = 35 + (i // 3) * 25

            # 根据阈值模式选择标签
            if self.threshold_mode in ['red_point1', 'red_point2']:
                label = param_labels[i]
            else:
                label = str(i)  # rect模式仍使用数字标号

            img.draw_string_advanced(x_pos, y_pos, 16, f"{label}:{value}", color=(0, 0, 255))

    def show_message(self, img, message):
        """显示状态消息"""
        temp_img = img.copy()
        temp_img.draw_rectangle(200, 200, 300, 40, color=self.button_color, thickness=2, fill=True)
        temp_img.draw_string_advanced(200, 200, 30, message, color=self.text_color)
        self._display_image(temp_img)
        time.sleep_ms(1500)

    def _display_image(self, img):
        """图像显示函数"""
        if img.height() > 480 or img.width() > 800:
            scale = max(img.height() // 480, img.width() // 800) + 1
            img.midpoint_pool(scale, scale)
        img.compress_for_ide()
        Display.show_image(img, x=(800-img.width())//2, y=(480-img.height())//2)

    def handle_button_action(self, button, img):
        """处理按钮动作"""
        if button == "return":
            print("点击返回按钮")
            return True  # 退出调整模式

        elif button == "change_mode":
            # 切换阈值模式
            threshold_modes = list(self.threshold_dict.keys())
            current_index = threshold_modes.index(self.threshold_mode)
            self.threshold_mode = threshold_modes[(current_index + 1) % len(threshold_modes)]
            self.load_existing_threshold()  # 加载新模式的阈值
            print(f"切换到模式: {self.threshold_mode}")
            self.show_message(img, f"模式:{self.threshold_mode}")

        elif button == "reset":
            # 重置滑块到原始值
            self.load_existing_threshold()  # 重新加载原始值
            print("重置到原始值")
            self.show_message(img, "重置原始值")

        elif button == "save":
            # 保存当前阈值（固定更新第一个阈值）
            if self.threshold_mode in self.threshold_dict:
                thresholds = self.threshold_dict[self.threshold_mode]
                if len(thresholds) > 0:
                    if self.threshold_mode in ['red_point1', 'red_point2']:
                        thresholds[0] = tuple(self.threshold_current)
                    elif self.threshold_mode == 'rect':
                        thresholds[0] = self.threshold_current[:2]
                    print(f"更新第一个阈值: {self.threshold_current}")
                    self.show_message(img, "更新成功")
                else:
                    self.show_message(img, "无阈值可更新")

        elif button and button.isdigit():
            # 滑块调整
            button_num = int(button)
            param_index = button_num % 6
            old_value = self.threshold_current[param_index]

            # 根据阈值模式设置调整范围
            if self.threshold_mode in ['red_point1', 'red_point2']:
                min_val, max_val = -128, 127
            else:
                min_val, max_val = 0, 255

            if button_num >= 6:  # 右侧按钮（增加）
                self.threshold_current[param_index] = min(max_val, self.threshold_current[param_index] + 2)
            else:  # 左侧按钮（减少）
                self.threshold_current[param_index] = max(min_val, self.threshold_current[param_index] - 2)

            new_value = self.threshold_current[param_index]

            # 根据阈值模式显示不同的参数名称
            if self.threshold_mode in ['red_point1', 'red_point2']:
                param_names = ['l_min', 'l_max', 'a_min', 'a_max', 'b_min', 'b_max']
                param_name = param_names[param_index]
                print(f"调整参数{param_name}: {old_value} -> {new_value}")
            else:
                print(f"调整参数{param_index}: {old_value} -> {new_value}")

        return False  # 继续调整模式

    def run_adjustment_mode(self):
        """运行阈值调整模式"""
        print("进入阈值调整模式")

        # 调试阈值数据
        self.debug_threshold_data()

        # 加载现有阈值进行微调，而不是清空
        self.load_existing_threshold()
        print(f"当前模式: {self.threshold_mode}")
        print(f"当前阈值: {self.threshold_current}")

        # 添加退出标志
        self.is_running = True
        touch_debounce = 0  # 触摸防抖计数器

        try:
            while self.is_running:
                # 重新创建GUI画布（每次循环都重新创建以确保界面更新）
                img = self.create_gui_canvas()

                # 处理图像预览
                try:
                    self.process_image_preview(img)
                except Exception as e:
                    print(f"图像预览错误: {e}")
                    # 如果图像预览失败，显示错误信息
                    img.draw_string_advanced(200, 240, 30, "图像错误", color=(255, 0, 0))

                # 检测触摸输入（添加防抖）
                if touch_debounce > 0:
                    touch_debounce -= 1
                else:
                    points = self.tp.read()
                    if len(points) > 0:
                        button = self.identify_button(points[0].x, points[0].y)
                        if button:
                            print(f"按钮点击: {button}")
                            should_exit = self.handle_button_action(button, img)
                            if should_exit:
                                self.is_running = False
                                break
                            touch_debounce = 10  # 设置防抖延时

                # 显示界面
                self._display_image(img)

                # 添加小延时避免CPU占用过高
                time.sleep_ms(50)

        finally:
            print("退出阈值调整模式")

sensor = None

try:
    sensor = Sensor(width=1920, height=1080)
    sensor.reset()

    # 鼠标悬停在函数上可以查看允许接收的参数
    sensor.set_framesize(width=1920, height=1080)
    sensor.set_pixformat(Sensor.RGB565)

    Display.init(Display.ST7701, to_ide=True, width=800, height=480)
    # 初始化媒体管理器
    MediaManager.init()
    # 启动 sensor
    sensor.run()

    fpioa = FPIOA()
    fpioa.help()
    # 设置按键
    fpioa.set_function(53, FPIOA.GPIO53)
    key = Pin(53, Pin.IN, Pin.PULL_DOWN)

    #设置舵机和激光笔
    fpioa.set_function(33, FPIOA.GPIO33)
    fpioa.set_function(46, FPIOA.PWM2)
    fpioa.set_function(42, FPIOA.PWM0)
    pin = Pin(33, Pin.OUT)
    pin.value(0)

    clock = time.clock()

    # 状态标识，死循环中会根据不同的flag值执行相应的逻辑
    # flag = 1则识别激光点
    # flag = 2则可以调整阈值
    flag = 0

    # 裁剪图像的ROI，格式为(x, y, w, h)，推荐使用480*480的大小，此大小性能高，而且刚好可以铺满LCD屏幕
    cut_roi = (540, 300, 480, 480)

    # 向屏幕输出图像，脱机运行时可以选择注释img.compress_for_ide()来提升性能
    def show_img_2_screen():
        global img
        if(img.height()>480 or img.width() > 800):
            scale = max(img.height() // 480, img.width() // 800) + 1
            img.midpoint_pool(scale, scale)
        img.compress_for_ide()
        Display.show_image(img, x=(800-img.width())//2, y=(480-img.height())//2)

    # 触摸计数器，达到一定的数值后开启阈值编辑模式，防止误触
    touch_counter = 0

    # 触摸屏初始化
    tp = TOUCH(0)

    # 存储阈值 - 拆分为独立的阈值类型
    threshold_dict = {
        'rect': [(59, 246)],
        'red_point1': [(47, 80, 9, 91, -55, 63)],      # 第1个红点阈值
        'red_point2': [(16, 37, 23, 74, -48, 52)]       # 第2个红点阈值
    }
    # 清空阈值（可以注释掉，这里只是为了演示阈值编辑功能）
#    threshold_dict['rect'] = []
#    threshold_dict['red_point'] = []

    while True:
        clock.tick()
        os.exitpoint()

        if flag == 2:
            try:
                # 打开激光笔
                pin.value(1)
                print("启动阈值调整功能")

                # 创建阈值调节器实例
                adjuster = ThresholdAdjuster(sensor, tp, threshold_dict, cut_roi, show_img_2_screen)

                # 运行阈值调整模式
                adjuster.run_adjustment_mode()

                print("阈值调整完成")
                print(f"最终阈值: {threshold_dict}")

            except Exception as e:
                print(f"阈值调整模式错误: {e}")
            finally:
                # 调整完成后返回正常模式
                flag = 0
                time.sleep_ms(1000)  # 减少等待时间

        else:
            img = sensor.snapshot()  # 修复：移除chn参数
            img = img.copy(roi=cut_roi)
            img.draw_string_advanced(50, 50, 80, "fps: {}".format(clock.fps()), color=(255, 0, 0))
            show_img_2_screen()

        # 实现一个长按屏幕进入阈值编辑模式的效果
        points = tp.read()
        if len(points) > 0:
            touch_counter += 1
            if touch_counter > 20:
                flag = 2
            print(points[0].x)
        else:
            touch_counter -= 2
            touch_counter = max(0, touch_counter)

except KeyboardInterrupt as e:
    print("用户停止: ", e)
except BaseException as e:
    print(f"异常: {e}")
finally:
    if sensor:
        sensor.stop()
    try:
        from media.display import Display
        from media.media import MediaManager
        Display.deinit()
        MediaManager.deinit()
    except:
        pass
    os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
    time.sleep_ms(100)

