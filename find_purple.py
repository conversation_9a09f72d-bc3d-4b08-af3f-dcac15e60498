import time
import os
from media.sensor import *
from media.display import *
from media.media import *
from time import ticks_ms

from machine import FPIOA
from machine import Pin
from machine import UART

sensor = None

# --- MODIFICATION START ---
# 将竞赛流程设置为1，以直接测试矩形识别
contest_flow = 0

sensor = None
osd_img = None
clock = None
rgb888p_size = [800,480]
display_size = [800,480]
draw_img = None
# --- MODIFICATION END ---

DISPLAY_WIDTH = 800
DISPLAY_HEIGHT = 480
roi = (233, 71, 319, 400)

# Variables for smoothing
prev_blob_data = {'x': -1, 'y': -1, 'w': -1, 'h': -1}
alpha = 1 # Smoothing factor

draw_img_stop = None
plaser_x = 0
plaser_y = 0

uart3 = None

def media_init():
    global sensor,osd_img,rgb888p_size,display_size,draw_img,uart2,uart3,clock

    fpioa = FPIOA()
    fpioa.set_function(11, FPIOA.UART2_TXD)
    fpioa.set_function(12, FPIOA.UART2_RXD)
    uart2 = UART(UART.UART2, 115200)

    Display.init(Display.ST7701, width = DISPLAY_WIDTH, height = DISPLAY_HEIGHT, to_ide =True, osd_num=2)
    sensor = Sensor(fps=30)
    sensor.reset()
    sensor.set_framesize(w = 800, h = 480,chn=CAM_CHN_ID_0)
    sensor.set_pixformat(Sensor.YUV420SP)
    sensor.set_framesize(w =800, h = 480, chn=CAM_CHN_ID_1)
    sensor.set_pixformat(Sensor.RGB565, chn=CAM_CHN_ID_1)

    sensor_bind_info = sensor.bind_info(x = 0, y = 0, chn = CAM_CHN_ID_0)
    Display.bind_layer(**sensor_bind_info, layer = Display.LAYER_VIDEO1)
    draw_img = image.Image(800, 480, image.ARGB8888)
    MediaManager.init()
    sensor.run()
    clock = time.clock()

def media_deinit():
    global sensor
    os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
    sensor.stop()
    Display.deinit()
    MediaManager.deinit()
def find_bolbs(img):
        global prev_blob_data,plaser_x,plaser_y
        # 寻找色块，参数依次为颜色阈值, 是否反转，roi, x_stride, y_stride, pixels_threshold, margin(是否合并)
        purple_blobs = img.find_blobs([(62, 75, 5, 50, -54, -16), (14, 67, 7, 127, -112, -3)], False, \
                                   roi=roi, x_stride=1, y_stride=1, \
                                   pixels_threshold=0, margin=True)

        if purple_blobs:
            current_blob = purple_blobs[0]
            if prev_blob_data['x'] == -1:  # First detection
                smoothed_x = current_blob.x()
                smoothed_y = current_blob.y()
                smoothed_w = current_blob.w()
                smoothed_h = current_blob.h()
            else:
                smoothed_x = int(alpha * current_blob.x() + (1 - alpha) * prev_blob_data['x'])
                smoothed_y = int(alpha * current_blob.y() + (1 - alpha) * prev_blob_data['y'])
                smoothed_w = int(alpha * current_blob.w() + (1 - alpha) * prev_blob_data['w'])
                smoothed_h = int(alpha * current_blob.h() + (1 - alpha) * prev_blob_data['h'])

            # Update previous blob data
            prev_blob_data['x'] = smoothed_x
            prev_blob_data['y'] = smoothed_y
            prev_blob_data['w'] = smoothed_w
            prev_blob_data['h'] = smoothed_h

            #draw_img.draw_circle(smoothed_x,smoothed_y, 3, color=(0, 255, 0), thickness=2, fill=False)
            purple_centerX = smoothed_x + smoothed_w / 2.0
            purple_centerY = smoothed_y + smoothed_h / 2.0

            # 更新激光点坐标
            plaser_x = int(purple_centerX)
            plaser_y = int(purple_centerY)

        else:
            prev_blob_data = {'x': -1, 'y': -1, 'w': -1, 'h': -1}

        return plaser_x,plaser_y

try:
    count = 0
    media_init()
    print("系统启动，开始自动检测矩形框...")

    while True:
        clock.tick()
        count  = count + 1
        if draw_img_stop:
            break
        os.exitpoint()
        img = sensor.snapshot(chn=CAM_CHN_ID_1)
        draw_img.clear()

        uart3_get = True
        if(contest_flow == 0):
            plaser_x,plaser_y= find_bolbs(img)
            if plaser_x > 0 and plaser_y > 0:
                #draw_img.draw_line(plaser_x, plaser_y, target_point[0], target_point[1], color=(0, 255, 255), thickness=1)
                draw_img.draw_circle(plaser_x,plaser_y,3,color = (255,0,0),thickness = 3,fill = True)
        # 绘制ROI区域
        draw_img.draw_rectangle(roi, color=(0, 0, 255), thickness=1, fill=False)
        draw_img.draw_string(10,10,f"fps:.{int(clock.fps())}",color = (255,0,0),scale = 4,mono_space = False)
        Display.show_image(draw_img, 0, 0, Display.LAYER_OSD1)
except KeyboardInterrupt as e:
    print("用户停止: ", e)
except BaseException as e:
    print(f"异常: {e}")
    import sys
    sys.print_exception(e)
    draw_img_stop = True
finally:
    media_deinit()
    time.sleep_ms(100)

